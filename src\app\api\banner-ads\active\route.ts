import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';

/**
 * GET /api/banner-ads/active
 * Get all currently active banner ads for public display
 * This endpoint is public and doesn't require authentication
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    const now = new Date();

    // Find active banner ads that are currently valid
    const activeBannerAds = await BannerAd.find({
      isActive: true,
      $or: [
        { startDate: { $lte: now } },
        { startDate: { $exists: false } }
      ],
      $or: [
        { endDate: { $gte: now } },
        { endDate: { $exists: false } },
        { endDate: null }
      ]
    })
    .select('title description imageUrl linkUrl bannerType styling priority analytics')
    .sort({ priority: -1, createdAt: -1 })
    .limit(5) // Limit to 5 active banners max
    .lean();

    // Track impressions for each banner
    if (activeBannerAds.length > 0) {
      const bannerIds = activeBannerAds.map(banner => banner._id);
      await BannerAd.updateMany(
        { _id: { $in: bannerIds } },
        { $inc: { 'analytics.impressions': 1 } }
      );
    }

    return NextResponse.json({
      banners: activeBannerAds,
      count: activeBannerAds.length
    });

  } catch (error) {
    console.error('Error fetching active banner ads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch active banner ads' },
      { status: 500 }
    );
  }
}
