'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

interface BannerAd {
  _id: string;
  title: string;
  description?: string;
  imageUrl?: string;
  linkUrl?: string;
  bannerType: 'image' | 'solid-color';
  styling: {
    backgroundColor: string;
    textColor: string;
    titleSize: string;
    descriptionSize: string;
    borderRadius: string;
    padding: string;
    animation: 'none' | 'fadeIn' | 'slideIn' | 'bounce' | 'pulse';
    animationDuration: string;
    positions: ('top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections')[];
    layout: 'horizontal' | 'full-width' | 'centered';
    textAlign: 'left' | 'center' | 'right';
  };
  priority: number;
}

interface BannerDisplayProps {
  position?: 'top' | 'center' | 'bottom' | 'hero-overlay' | 'between-sections';
  className?: string;
}

export default function BannerDisplay({ position = 'top', className = '' }: BannerDisplayProps) {
  const [banners, setBanners] = useState<BannerAd[]>([]);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [loading, setLoading] = useState(true);
  const [dismissedBanners, setDismissedBanners] = useState<Set<string>>(new Set());
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Fetch active banners
  useEffect(() => {
    const fetchBanners = async () => {
      try {
        const response = await fetch('/api/banner-ads/active');
        if (response.ok) {
          const data = await response.json();
          // Filter banners by position and exclude dismissed ones
          const filteredBanners = data.banners.filter(
            (banner: BannerAd) =>
              banner.styling.positions?.includes(position) &&
              !dismissedBanners.has(banner._id) &&
              // Only exclude image banners with image errors, allow solid color banners
              (banner.bannerType === 'solid-color' || !imageErrors.has(banner._id))
          );
          console.log('Filtered banners for position', position, ':', filteredBanners);
          console.log('Image errors:', Array.from(imageErrors));
          console.log('Raw banners from API:', data.banners);

          // Debug each banner individually
          data.banners.forEach((banner: BannerAd, index: number) => {
            console.log(`Banner ${index}:`, {
              id: banner._id,
              title: banner.title,
              bannerType: banner.bannerType,
              imageUrl: banner.imageUrl,
              positions: banner.styling.positions,
              hasPosition: banner.styling.positions?.includes(position),
              isDismissed: dismissedBanners.has(banner._id),
              hasImageError: imageErrors.has(banner._id)
            });
          });
          setBanners(filteredBanners);
        }
      } catch (error) {
        console.error('Error fetching banners:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, [position, dismissedBanners, imageErrors]);

  // Cycle through banners if multiple exist
  useEffect(() => {
    if (banners.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % banners.length);
      }, 8000); // Change banner every 8 seconds

      return () => clearInterval(interval);
    }
  }, [banners.length]);

  // Track banner view
  const trackView = async (bannerId: string) => {
    try {
      await fetch(`/api/admin/banner-ads/${bannerId}/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'view' }),
      });
    } catch (error) {
      console.error('Error tracking banner view:', error);
    }
  };

  // Track banner click
  const trackClick = async (bannerId: string) => {
    try {
      await fetch(`/api/admin/banner-ads/${bannerId}/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'click' }),
      });
    } catch (error) {
      console.error('Error tracking banner click:', error);
    }
  };

  // Handle banner click
  const handleBannerClick = (banner: BannerAd) => {
    trackClick(banner._id);
    if (banner.linkUrl) {
      window.open(banner.linkUrl, '_blank', 'noopener,noreferrer');
    }
  };

  // Handle banner dismiss
  const handleDismiss = (bannerId: string) => {
    setDismissedBanners(prev => new Set([...prev, bannerId]));
    setBanners(prev => prev.filter(banner => banner._id !== bannerId));
  };

  // Track view when banner becomes visible
  useEffect(() => {
    if (banners.length > 0 && isVisible) {
      const currentBanner = banners[currentBannerIndex];
      if (currentBanner) {
        trackView(currentBanner._id);
      }
    }
  }, [banners, currentBannerIndex, isVisible]);

  if (loading || banners.length === 0 || !isVisible) {
    return null;
  }

  const currentBanner = banners[currentBannerIndex];

  // Animation variants
  const getAnimationVariants = (animation: string) => {
    switch (animation) {
      case 'fadeIn':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
      case 'slideIn':
        return {
          initial: { x: position === 'top' ? -100 : 100, opacity: 0 },
          animate: { x: 0, opacity: 1 },
          exit: { x: position === 'top' ? 100 : -100, opacity: 0 }
        };
      case 'bounce':
        return {
          initial: { scale: 0.8, opacity: 0 },
          animate: { scale: 1, opacity: 1, transition: { type: 'spring', bounce: 0.4 } },
          exit: { scale: 0.8, opacity: 0 }
        };
      case 'pulse':
        return {
          initial: { scale: 0.95, opacity: 0 },
          animate: { 
            scale: 1, 
            opacity: 1,
            transition: { 
              scale: { repeat: Infinity, repeatType: 'reverse', duration: 2 },
              opacity: { duration: 0.5 }
            }
          },
          exit: { scale: 0.95, opacity: 0 }
        };
      default:
        return {
          initial: {},
          animate: {},
          exit: {}
        };
    }
  };

  const animationVariants = getAnimationVariants(currentBanner.styling.animation);
  const animationDuration = parseFloat(currentBanner.styling.animationDuration);

  // Special styling for hero overlay position
  const isHeroOverlay = position === 'hero-overlay';
  const overlayStyles = isHeroOverlay ? {
    backgroundColor: `${currentBanner.styling.backgroundColor}CC`, // Add transparency
    backdropFilter: 'blur(8px)',
    border: `1px solid ${currentBanner.styling.textColor}20`
  } : {
    backgroundColor: currentBanner.styling.backgroundColor,
  };

  // Render banner content based on layout and type
  const renderBannerContent = () => {
    const layout = currentBanner.styling.layout || 'horizontal';
    const textAlign = currentBanner.styling.textAlign || 'left';
    const isImageBanner = currentBanner.bannerType === 'image' && currentBanner.imageUrl;

    const commonProps = {
      className: currentBanner.linkUrl ? 'cursor-pointer' : '',
      onClick: () => currentBanner.linkUrl && handleBannerClick(currentBanner)
    };

    // Full-width banner layout
    if (layout === 'full-width') {
      return (
        <div {...commonProps} className={`relative min-h-[120px] flex items-center ${commonProps.className}`}>
          {/* Background image for image banners */}
          {isImageBanner && !imageErrors.has(currentBanner._id) && (
            <div className="absolute inset-0 overflow-hidden" style={{ borderRadius: currentBanner.styling.borderRadius }}>
              <Image
                src={currentBanner.imageUrl!}
                alt={currentBanner.title}
                fill
                className="object-cover"
                onError={(e) => {
                  console.error('Image failed to load:', currentBanner.imageUrl, e);
                  setImageErrors(prev => new Set([...prev, currentBanner._id]));
                }}
                onLoad={() => console.log('Image loaded successfully:', currentBanner.imageUrl)}
                unoptimized={true}
              />
              <div className="absolute inset-0 bg-black/40" />
            </div>
          )}

          {/* Content overlay */}
          <div className={`relative z-10 w-full px-6 py-4 text-${textAlign}`}>
            <h3
              className="font-bold mb-2"
              style={{
                color: currentBanner.styling.textColor,
                fontSize: currentBanner.styling.titleSize,
                textShadow: isImageBanner ? '0 2px 4px rgba(0,0,0,0.5)' : 'none'
              }}
            >
              {currentBanner.title}
            </h3>
            {currentBanner.description && (
              <p
                className="opacity-90 max-w-2xl"
                style={{
                  color: currentBanner.styling.textColor,
                  fontSize: currentBanner.styling.descriptionSize,
                  textShadow: isImageBanner ? '0 1px 2px rgba(0,0,0,0.5)' : 'none'
                }}
              >
                {currentBanner.description}
              </p>
            )}
            {currentBanner.linkUrl && (
              <div className="mt-3">
                <span
                  className="inline-block text-sm font-medium px-4 py-2 rounded border opacity-80 hover:opacity-100 transition-opacity"
                  style={{
                    color: currentBanner.styling.textColor,
                    borderColor: currentBanner.styling.textColor,
                    backgroundColor: isImageBanner ? 'rgba(0,0,0,0.3)' : 'transparent'
                  }}
                >
                  Learn More →
                </span>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Centered layout
    if (layout === 'centered') {
      return (
        <div {...commonProps} className={`text-center py-8 ${commonProps.className}`}>
          {isImageBanner && !imageErrors.has(currentBanner._id) && (
            <div className="mb-4 flex justify-center">
              <div className="relative w-24 h-24 rounded-lg overflow-hidden">
                <Image
                  src={currentBanner.imageUrl!}
                  alt={currentBanner.title}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    console.error('Image failed to load (centered):', currentBanner.imageUrl, e);
                    setImageErrors(prev => new Set([...prev, currentBanner._id]));
                  }}
                  onLoad={() => console.log('Image loaded successfully (centered):', currentBanner.imageUrl)}
                  unoptimized={true}
                />
              </div>
            </div>
          )}
          <h3
            className="font-semibold mb-2"
            style={{
              color: currentBanner.styling.textColor,
              fontSize: currentBanner.styling.titleSize,
            }}
          >
            {currentBanner.title}
          </h3>
          {currentBanner.description && (
            <p
              className="opacity-90 max-w-md mx-auto"
              style={{
                color: currentBanner.styling.textColor,
                fontSize: currentBanner.styling.descriptionSize,
              }}
            >
              {currentBanner.description}
            </p>
          )}
          {currentBanner.linkUrl && (
            <div className="mt-4">
              <span
                className="inline-block text-sm font-medium px-4 py-2 rounded border opacity-80 hover:opacity-100 transition-opacity"
                style={{
                  color: currentBanner.styling.textColor,
                  borderColor: currentBanner.styling.textColor,
                }}
              >
                Learn More →
              </span>
            </div>
          )}
        </div>
      );
    }

    // Default horizontal layout
    return (
      <div {...commonProps} className={`flex items-center gap-4 ${commonProps.className}`}>
        {/* Banner image */}
        {isImageBanner && (
          <div className="relative flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded overflow-hidden">
            {imageErrors.has(currentBanner._id) ? (
              <div
                className="w-full h-full flex items-center justify-center text-xs font-medium"
                style={{
                  backgroundColor: currentBanner.styling.backgroundColor,
                  color: currentBanner.styling.textColor
                }}
              >
                AD
              </div>
            ) : (
              <Image
                src={currentBanner.imageUrl!}
                alt={currentBanner.title}
                fill
                className="object-cover"
                sizes="(max-width: 640px) 64px, (max-width: 768px) 80px, 96px"
                onError={(e) => {
                  console.error('Image failed to load (horizontal):', currentBanner.imageUrl, e);
                  setImageErrors(prev => new Set([...prev, currentBanner._id]));
                }}
                onLoad={() => console.log('Image loaded successfully (horizontal):', currentBanner.imageUrl)}
                unoptimized={true}
              />
            )}
          </div>
        )}

        {/* Banner text */}
        <div className={`flex-1 min-w-0 text-${textAlign}`}>
          <h3
            className="font-semibold truncate"
            style={{
              color: currentBanner.styling.textColor,
              fontSize: currentBanner.styling.titleSize,
            }}
          >
            {currentBanner.title}
          </h3>
          {currentBanner.description && (
            <p
              className="mt-1 opacity-90 line-clamp-2"
              style={{
                color: currentBanner.styling.textColor,
                fontSize: currentBanner.styling.descriptionSize,
              }}
            >
              {currentBanner.description}
            </p>
          )}
        </div>

        {/* Link indicator */}
        {currentBanner.linkUrl && (
          <div className="flex-shrink-0">
            <div
              className="text-sm font-medium px-3 py-1 rounded border opacity-80 hover:opacity-100 transition-opacity"
              style={{
                color: currentBanner.styling.textColor,
                borderColor: currentBanner.styling.textColor,
              }}
            >
              Learn More →
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={`${currentBanner._id}-${currentBannerIndex}`}
        className={`relative w-full ${className} ${isHeroOverlay ? 'backdrop-blur-sm' : ''}`}
        initial={animationVariants.initial}
        animate={animationVariants.animate}
        exit={animationVariants.exit}
        transition={{ duration: animationDuration }}
        style={{
          ...overlayStyles,
          borderRadius: currentBanner.styling.borderRadius,
          padding: currentBanner.styling.padding,
        }}
      >


        {/* Banner content */}
        {renderBannerContent()}

        {/* Multiple banners indicator */}
        {banners.length > 1 && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {banners.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-opacity ${
                  index === currentBannerIndex ? 'opacity-100' : 'opacity-40'
                }`}
                style={{ backgroundColor: currentBanner.styling.textColor }}
              />
            ))}
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}
